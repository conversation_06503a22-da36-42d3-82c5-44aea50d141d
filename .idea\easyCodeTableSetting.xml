<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="qis.dbo.chemical">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="organizationId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="attributeId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="examineDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="shift" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="staff" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="departmentCode" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="processSetName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="processName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="productSetName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="productName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="testSetName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="testName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sampleSize" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="layerNumber" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="upperLimit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="medianSpecificantion" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="downLimit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="examine1" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="examine2" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="examine3" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdBy" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="lastUpdatedBy" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="creationDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="lastUpdateDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="frequency" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="frequencyUnit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="slotBodyName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectTeamCode" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectTeamName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="testCode" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="warningUpperLimit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="warningMid" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="warningLowerLimit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectUnit" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="Chemical" />
              <option name="preName" value="" />
              <option name="saveModelName" value="token" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="./src/main/java" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>