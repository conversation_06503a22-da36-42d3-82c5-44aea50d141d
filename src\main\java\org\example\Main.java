package org.example;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.apache.pulsar.client.api.*;
import org.apache.pulsar.shade.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;


public class Main {
    private static final String BROKER_SERVICE_URL = "pulsar://pulsar.scc.com:6650";
    private static final String MEDICINE_TOPIC = "persistent://spc/302-interface/labMedicineData";
    private static final String TOKEN =
            "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzAyIn0.bStvCEst8V8SQWxUb4beeqCDKtBsCRI6zu7CsL6-VHU";
    public static Thread taskThread;
    public static String filePath;
    public static boolean isDateFilterActive = false;
    private static volatile boolean isRunning = false;
    private static volatile boolean isPaused = false;
    private static LocalDate startDate;
    private static LocalDate endDate;
    private static PulsarClient pulsarClient;
    private static Consumer<byte[]> consumer;
    private static volatile boolean isTimerTaskRunning = true; // 新变量控制定时任务
    private static final HikariDataSource dataSource;
    private static final HikariDataSource dataSourceCloud;
    // 新增变量来累积所有数据

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("****************************************************************");
        config.setUsername("sa");
        config.setPassword("root1234");
        config.setMaximumPoolSize(100); // 根据需要调整池大小
        config.setMinimumIdle(5);
        config.setIdleTimeout(30000);
        config.setMaxLifetime(60000);
        dataSource = new HikariDataSource(config);
    }

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*************************************************");
        config.setUsername("spc");
        config.setPassword("spc123");
        config.setMaximumPoolSize(100); // 根据需要调整池大小
        config.setMinimumIdle(5);
        config.setIdleTimeout(30000);
        config.setMaxLifetime(60000);
        config.setPoolName("CloudDataSourcePool"); // 设置数据源的名称
        config.setRegisterMbeans(true); // 启用JMX监控
        dataSourceCloud = new HikariDataSource(config);
    }

    /**
     * 主方法，程序的入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 定期检查新数据
        runMainLogic();
    }

    /**
     * 设置处理日期范围
     *
     * @param start 开始日期
     * @param end   结束日期
     */
    public static void setProcessingDates(LocalDate start, LocalDate end) {
        startDate = start;
        endDate = end;
    }

    private static void processMessage(Connection connection, ObjectMapper objectMapper, Message<byte[]> msg) {
        try {
            Chemical chemical = objectMapper.readValue(msg.getData(), Chemical.class);
            insertDataIntoDatabase(connection, chemical);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 修改 insertDataIntoDatabase 方法以插入数据
    private static void insertDataIntoDatabase(Connection connection, Chemical entity) {
        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "department_code, process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,is_exported,warning_upper_limit," +
                "warning_mid,warning_lower_limit,attribute1,attribute2,attribute3) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {

            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getDepartmentCode());
            preparedStatement.setString(8, entity.getProcessSetName());
            preparedStatement.setString(9, entity.getProcessName());
            preparedStatement.setString(10, entity.getProductSetName());
            preparedStatement.setString(11, entity.getProductName());
            preparedStatement.setString(12, entity.getTestSetName());
            preparedStatement.setString(13, entity.getTestName().trim());
            preparedStatement.setString(14, entity.getSampleSize());
            preparedStatement.setString(15, entity.getLayerNumber());
            preparedStatement.setString(16, entity.getUpperLimit());
            preparedStatement.setString(17, entity.getMedianSpecification());
            preparedStatement.setString(18, entity.getDownLimit());
            preparedStatement.setString(19, entity.getExamine1());
            preparedStatement.setString(20, entity.getExamine2());
            preparedStatement.setString(21, entity.getCreatedBy());
            preparedStatement.setString(22, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(23, entity.getCreationDate());
            preparedStatement.setTimestamp(24, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 25, entity.getStatus());
            preparedStatement.setString(26, entity.getFrequency());
            preparedStatement.setString(27, entity.getFrequencyUnit());
            preparedStatement.setString(28, entity.getSlotBodyName());
            preparedStatement.setString(29, entity.getProjectTeamCode());
            preparedStatement.setString(30, entity.getProjectTeamName());
            preparedStatement.setString(31, entity.getTestCode());
            preparedStatement.setString(32, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(33, entity.getAdjustmentMid());
            preparedStatement.setString(34, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(35, entity.getProjectUnit());
            preparedStatement.setTimestamp(36, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setBoolean(37, false);
            preparedStatement.setString(38, entity.getWarningUpperLimit());
            preparedStatement.setString(39, entity.getWarningMid());
            preparedStatement.setString(40, entity.getWarningLowerLimit());
            preparedStatement.setString(41, entity.getAttribute1());
            preparedStatement.setString(42, entity.getAttribute2());
            preparedStatement.setString(43, entity.getAttribute3());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                System.out.println("数据成功插入。");
            }
        } catch (SQLException e) {
            System.out.println("插入数据时出错：" + e.getMessage());
            handleSQLException(e);
        }
    }

    private static void setNullableLong(PreparedStatement preparedStatement, int parameterIndex, Long value) throws SQLException {
        if (value != null) {
            preparedStatement.setLong(parameterIndex, value);
        } else {
            preparedStatement.setNull(parameterIndex, Types.BIGINT);
        }
    }

    private static void handleSQLException(SQLException e) {
        if (e.getErrorCode() == 2627) {
            System.out.println("数据插入失败，该数据已存在。");
        } else {
            e.printStackTrace();
        }
    }

    private static String getF_PRCSValueForDepartment(Connection remoteConnection, String departmentName) {
        String sql = "SELECT F_PRCS FROM SPC.PRCS_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, departmentName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_PRCS");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PRCS 值
    }

    private static String getF_TESTValueForTestName(Connection remoteConnection, String testName) {
        String sql = "SELECT F_TEST FROM SPC.TEST_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, testName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_TEST");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_TEST 值
    }

    private static String getF_PARTValueForProductName(Connection remoteConnection, String productName) {
        String sql = "SELECT F_PART FROM SPC.PART_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, productName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
//                    System.out.println(productName);
                    return resultSet.getString("F_PART");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PART 值
    }

    private static CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName) {
        // 定义测试名与样本量的映射
        Map<String, Integer> sampleSizeMap = new HashMap<String, Integer>();
        sampleSizeMap.put("PD全线微蚀量", 3);
        sampleSizeMap.put("TR微蚀微蚀量", 5);
        sampleSizeMap.put("TR微蚀微蚀量(2.1线速)", 5);
        sampleSizeMap.put("TROSPOSP膜厚", 3);
        sampleSizeMap.put("TROSPOSP膜厚(2.1线速)", 3);
        double intermediateValue = 6 * controlLimits.fSp;
        // 如果测试名在映射中，根据样本量调整intermediateValue的计算
        if (sampleSizeMap.containsKey(testName)) {
            int sampleSize = sampleSizeMap.get(testName);
            intermediateValue = intermediateValue / Math.sqrt(sampleSize);
        }
        double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
        double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
        double lcl1 = 0.833 * lowerControlLimit + 0.167 * upperControlLimit;
        double ucl1 = 0.167 * lowerControlLimit + 0.833 * upperControlLimit;
        return new CalculatedValues(upperControlLimit, lowerControlLimit, lcl1, ucl1);
    }

    private static double calculateExamineValue(double examine1, CalculatedValues calculatedValues) {
        if (examine1 < calculatedValues.lcl1) {
            return calculatedValues.lcl1 + (calculatedValues.lcl1 - examine1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        } else if (examine1 > calculatedValues.ucl1) {
            return calculatedValues.ucl1 - (examine1 - calculatedValues.ucl1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        }
        return examine1;
    }

    public static void processChemicalData(Connection localConnection, Connection remoteConnection,
                                           String csvFilePath, boolean isDateFilterActive) throws SQLException {
        String exportCheck = isDateFilterActive ? "" : "AND COALESCE(is_exported, 0) = 0 AND COALESCE(not_process, 0) = 0";
        String localSql = (startDate != null && endDate != null)
                ? "SELECT * FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " +
                "WHERE examine_date >= ? AND examine_date <= ? " + exportCheck
                :
                "SELECT * FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " + exportCheck;
        String individualFilePath = "";

        // 获取总记录数
        int totalRecords = getTotalRecordCount(localConnection, isDateFilterActive);
        int processedRecords = 0;

        try (PreparedStatement localPreparedStatement = localConnection.prepareStatement(localSql)) {
            if (startDate != null && endDate != null) {
                localPreparedStatement.setTimestamp(1, Timestamp.valueOf(startDate.atStartOfDay()));
                localPreparedStatement.setTimestamp(2, Timestamp.valueOf(endDate.atTime(23, 59, 59)));
            }
            try (ResultSet localResultSet = localPreparedStatement.executeQuery()) {
                while (localResultSet.next() && isRunning) {
                    // 如果任务被暂停，等待
                    if (isPaused) {
                        try {
                            Thread.sleep(500); // 等待 500 毫秒，避免 CPU 占用过高
                            continue;
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                    if (localResultSet.getString("upper_limit") == null || localResultSet.getString("upper_limit").equals("null")) {
                        continue;
                    }
                    String id = localResultSet.getString("id");
                    if (localResultSet.getDate("examine_date") == null) {
                        continue; // Skip this record if examine_date is null
                    }

                    System.out.println(id);
                    String examineDate = localResultSet.getString("examine_date");
                    String formattedDate = examineDate.replace(" ", "_").replace(":", "_");
                    individualFilePath = createIndividualFileName(csvFilePath, id, formattedDate);
                    boolean isExported = exportDataToIndividualFile(localResultSet, individualFilePath,
                            remoteConnection, localConnection);

                    if (isExported) {
                        markAsExported(localConnection, id); // 只有成功导出时才标记为已导出
                    } else {
                        // 如果无法处理，将not_process标记为1
                        markAsNotProcess(localConnection, id);
                    }
                    // 更新已处理记录数
                    processedRecords++;
                    // 计算进度
                    double progress = (double) processedRecords / totalRecords;
                    // 更新进度条
                    JavaFXApp.updateProgressBar(progress);
                }
            }
        } catch (SQLException | IOException e) {
            e.printStackTrace();
            JavaFXApp.showErrorAlert("数据处理任务发生错误：" + e.getMessage());
        }
    }

    private static int getTotalRecordCount(Connection localConnection, boolean isDateFilterActive) throws SQLException {
        String exportCheck = isDateFilterActive ? "" : "AND COALESCE(is_exported, 0) = 0 AND COALESCE(not_process, 0) = 0";
        String countSql = (startDate != null && endDate != null)
                ? "SELECT COUNT(*) FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " +
                "WHERE examine_date >= ? AND examine_date <= ? " + exportCheck
                :
                "SELECT COUNT(*) FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " + exportCheck;

        try (PreparedStatement countStmt = localConnection.prepareStatement(countSql)) {
            if (startDate != null && endDate != null) {
                countStmt.setTimestamp(1, Timestamp.valueOf(startDate.atStartOfDay()));
                countStmt.setTimestamp(2, Timestamp.valueOf(endDate.atTime(23, 59, 59)));
            }
            try (ResultSet rs = countStmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    // 更新数据库，将not_process字段设置为1
    private static void markAsNotProcess(Connection localConnection, String id) throws SQLException {
        String updateSql = "UPDATE dbo.chemical SET not_process = 1 WHERE id = ?";
        try (PreparedStatement updateStmt = localConnection.prepareStatement(updateSql)) {
            updateStmt.setString(1, id);
            updateStmt.executeUpdate();
        }
    }

    private static void markAsExported(Connection connection, String id) throws SQLException {
        String updateSql = "UPDATE dbo.chemical SET is_exported = 1 WHERE id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(updateSql)) {
            preparedStatement.setString(1, id);
            preparedStatement.executeUpdate();
        }
    }

    private static boolean exportDataToIndividualFile(ResultSet resultSet, String filePath,
                                                      Connection remoteConnection, Connection localConnection) throws SQLException, IOException {
        String result = extractDataLine(resultSet, remoteConnection, localConnection);
        if (result != null) {
            // 调用插入数据库的方法
            String examine1 = resultSet.getString("examine1");
            if (examine1 == null) {
                return false;
            }
            Chemical entity = resultToChemical(result, examine1); // 这个方法可以从result创建一个Chemical对象
            insertDataIntoChemicalYsTable(localConnection, entity);
            return true;
        }
        return false;
    }

    private static String createIndividualFileName(String baseFilePath, String id, String examineDate) {
        return baseFilePath + "\\LabMedicineSPC_" + id + "_" + examineDate + ".csv";
    }

    private static String extractDataLine(ResultSet localResultSet, Connection remoteConnection, Connection localConnection) throws SQLException {
        // 从 resultSet 提取数据并格式化为一行 CSV 字符串
        String id = localResultSet.getString("id");
        String organizationId = localResultSet.getString("organization_id");
        String attributeId = localResultSet.getString("attribute_id");
        String examineDate = localResultSet.getString("examine_date");
        String shift = localResultSet.getString("shift");
        String staff = localResultSet.getString("staff");
        String processSetName = localResultSet.getString("department_code");
        String processName = localResultSet.getString("department_name");
        String productSetName = localResultSet.getString("product_set_name");
        String productName = localResultSet.getString("process_name");
        String testSetName = localResultSet.getString("test_set_name");
        String testName = localResultSet.getString("test_name");
        String sampleSize = localResultSet.getString("sample_size");
        String layerNumber = localResultSet.getString("layer_number");
        String upperLimit = localResultSet.getString("upper_limit");
        String medianSpecification = localResultSet.getString("median_specification");
        String downLimit = localResultSet.getString("down_limit");
        double examine2 = localResultSet.getDouble("examine2");
        String createdBy = localResultSet.getString("created_by");
        String lastUpdatedBy = localResultSet.getString("last_updated_by");
        String creationDate = localResultSet.getString("creation_date");
        String lastUpdateDate = localResultSet.getString("last_update_date");
        String status = localResultSet.getString("status");
        String frequency = localResultSet.getString("frequency");
        String frequencyUnit = localResultSet.getString("frequency_unit");
        String slotBodyName = localResultSet.getString("slot_body_name");
        String projectTeamCode = localResultSet.getString("project_team_code");
        String projectTeamName = localResultSet.getString("project_team_name");
        String testCode = localResultSet.getString("test_code");
        String adjustmentUpperLimit = localResultSet.getString("adjustment_upper_limit");
        String adjustmentMid = localResultSet.getString("adjustment_mid");
        String adjustmentLowerLimit = localResultSet.getString("adjustment_lower_limit");
        String projectUnit = localResultSet.getString("project_unit");
        String departmentName = localResultSet.getString("department_name");
        double examine1 = localResultSet.getDouble("examine1");
        String warningUpperLimit = localResultSet.getString("warning_upper_limit");
        String warningMid = localResultSet.getString("warning_mid");
        String warningLowerLimit = localResultSet.getString("warning_lower_limit");

        // 检查是否存在于rule表中
        int onlyUpperLimit = getOnlyUpperLimitFlag(localConnection, productName, processName, testName);
        if (onlyUpperLimit == 1 || onlyUpperLimit == 2) {
            StringBuilder csvLine = new StringBuilder();
            csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                    .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                    .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                    .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                    .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                    .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                    .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                    .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                    .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                    .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                    .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                    .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
            return csvLine.toString();
        }
        // 获取远程数据库中的 F_PRCS, F_TEST, F_PART 值
        String fTestValue = getF_TESTValueForTestName(remoteConnection, testName);
        if (fTestValue == null) {
            return null;
        }
        String fPrcsValue = getF_PRCSValueForDepartment(remoteConnection, departmentName);
        if (fPrcsValue == null) {
            return null;
        }
        String fPartValue = getF_PARTValueForProductName(remoteConnection, productName);
        if (fPartValue == null) {
            return null;
        }

        // 获取控制限制
        ControlLimits controlLimits = ControlLimitsCache.getControlLimits(remoteConnection, fPrcsValue, fTestValue,
                fPartValue);

        if (controlLimits != null) {
            Double adjustedExamine1;
            // 在计算控制值之前
            if ("LV蓬松膨胀剂E".equals(testName)) {
                adjustedExamine1 = handleSpecialCaseForLV(String.valueOf(examine2), examine1);
            } else {
                // 计算控制值
                CalculatedValues calculatedValues = calculateControlValues(controlLimits, testName);
                System.out.println("Calculated Values: " + calculatedValues.lcl1 + ", " + calculatedValues.ucl1);
                adjustedExamine1 = calculateExamineValue(examine1, calculatedValues);
            }
            double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedExamine1 * examine2 / examine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度
            StringBuilder csvLine = new StringBuilder();
            if (!isPaused) {
                csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                        .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                        .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                        .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                        .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                        .append(medianSpecification).append(",").append(downLimit).append(",").append(roundExamineValue(adjustedExamine1))
                        .append(",").append(adjustedExamine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                        .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                        .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                        .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                        .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                        .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
                return csvLine.toString();
            } else {
                System.out.println("Examine1: " + examine1);
                csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                        .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                        .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                        .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                        .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                        .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                        .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                        .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                        .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                        .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                        .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                        .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
                return csvLine.toString();
            }
        }

        return null;
    }

    private static int getOnlyUpperLimitFlag(Connection localConnection, String productName, String processName, String testName) throws SQLException {
        String sql = "SELECT only_upper_limit FROM dbo.[rule] WHERE product_name = ? AND process_name = ? AND test_name = ?";
        try (PreparedStatement stmt = localConnection.prepareStatement(sql)) {
            stmt.setString(1, productName);
            stmt.setString(2, processName);
            stmt.setString(3, testName);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("only_upper_limit");
            }
        }
        return 0; // 默认返回0表示没有找到对应的规则，或者only_upper_limit不为1
    }

    private static double handleSpecialCaseForLV(String examine2Str, double examine1) {
        double examine2 = Double.parseDouble(examine2Str);
        double[] examine2Options = {14.0, 14.5, 15.0, 16.0};
        double[] examine1Mappings = {166.60, 172.55, 178.50, 190.40};
        double closest = examine2Options[0];
        double diff = Math.abs(examine2 - closest);

        for (int i = 1; i < examine2Options.length; i++) {
            double newDiff = Math.abs(examine2 - examine2Options[i]);
            if (newDiff < diff) {
                closest = examine2Options[i];
                diff = newDiff;
            }
        }

        for (int i = 0; i < examine2Options.length; i++) {
            if (closest == examine2Options[i]) {
                return examine1Mappings[i];
            }
        }

        return examine1; // 默认情况，返回原始 examine1
    }

    private static double roundExamineValue(double value) {
        return Math.round(value * 1000.0) / 1000.0;
    }

    public static void startTask() {
        if (isRunning) {
            return; // 如果任务已经在运行，则不重复启动
        }

        isRunning = true;
        isPaused = false;

        // 更新按钮状态
        JavaFXApp.updateButtonStates(true, false);

        taskThread = new Thread(Main::runMainLogic);
        taskThread.start();
    }

    public static void pauseTask() {
        isPaused = true;
        System.out.println("任务已暂停。");

        // 更新按钮状态
        JavaFXApp.updateButtonStates(true, true);
    }

    public static void stopTask() throws PulsarClientException {
        isRunning = false;
        isPaused = false; // 停止任务时，取消暂停状态
        if (taskThread != null) {
            taskThread.interrupt();
        }
        shutdown();
        System.out.println("任务已停止。");

        // 更新按钮状态
        JavaFXApp.updateButtonStates(false, false);
    }

    public static void stopApp() {
        if (taskThread != null) {
            taskThread.interrupt();
        }
        System.out.println("应用已关闭。");
    }

    private static void initializePulsarClient() {
        try {
            pulsarClient = PulsarClient.builder()
                    .serviceUrl(BROKER_SERVICE_URL)
                    .authentication(AuthenticationFactory.token(TOKEN))
                    .build();
        } catch (PulsarClientException e) {
            e.printStackTrace();
        }
    }

    private static void initializeConsumer() {
        try {
            String subscriptionName = "my-spc-" + Instant.now().toEpochMilli();
            consumer = pulsarClient.newConsumer(Schema.BYTES)
                    .subscriptionName("SZ302-Chemical")
                    .subscriptionType(SubscriptionType.Exclusive)
                    .subscriptionInitialPosition(SubscriptionInitialPosition.Earliest)
                    .topic(MEDICINE_TOPIC)
                    .subscribe();
        } catch (PulsarClientException e) {
            e.printStackTrace();
        }
    }

    public static void runMainLogic() {
        initializePulsarClient();
        initializeConsumer();

        // 定时任务执行器
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(3);

        // 定时任务：数据读取任务
        Runnable dataReadingTask = () -> {
            JavaFXApp.updateStatusLabel("数据读取任务开始");
            try (Connection connection = getDatabaseConnection()) {
                while (isRunning) {
                    if (isPaused) {
                        try {
                            Thread.sleep(500);
                            continue;
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                    Message<byte[]> msg = consumer.receive(5, TimeUnit.SECONDS); // 超时5秒
                    if (msg != null) {
                        System.out.println(new String(msg.getData()));
                        processMessage(connection, new ObjectMapper(), msg);
                        consumer.acknowledge(msg);
                    } else {
                        System.out.println("当前无新数据，等待下次检查");
                        break; // 如果没有消息，结束当前循环
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            JavaFXApp.updateStatusLabel("数据读取任务完成");
        };

        // 定时任务：日志读取任务
        Runnable logReadingTask = () -> {
            JavaFXApp.updateStatusLabel("日志读取任务开始");
            try (Connection localConnection = dataSource.getConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                printConnectionPoolStats();
                String logSql = "SELECT * FROM dbo.chemical_log WHERE processed = 0";
                try (PreparedStatement logStmt = localConnection.prepareStatement(logSql);
                     ResultSet logRs = logStmt.executeQuery()) {
                    while (logRs.next()) {
                        String chemicalId = logRs.getString("chemical_id");
//                        System.out.println(chemicalId);
                        boolean isExported = false;
                        String chemicalSql = "SELECT * FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code WHERE id = ?";
                        try (PreparedStatement chemicalStmt = localConnection.prepareStatement(chemicalSql)) {
                            chemicalStmt.setString(1, chemicalId);
                            try (ResultSet chemicalRs = chemicalStmt.executeQuery()) {
                                if (chemicalRs.next()) {
                                    if (chemicalRs.getString("upper_limit") == null || chemicalRs.getString("upper_limit").equals("null")) {
                                        continue;
                                    }
                                    if (chemicalRs.getDate("examine_date") == null) {
                                        continue; // Skip this record if examine_date is null
                                    }
                                    String id = chemicalRs.getString("id");
                                    String examineDate = chemicalRs.getString("examine_date");
                                    String formattedDate = examineDate.replace(" ", "_").replace(":", "_");
                                    String individualFilePath = createIndividualFileName(filePath, id, formattedDate);
                                    isExported = exportDataToIndividualFile(chemicalRs, individualFilePath, remoteConnection, localConnection);
                                    if (isExported) {
                                        markAsExported(localConnection, id); // 标记为已导出
                                    }
                                }
                            }
                        }
                        // 更新 log_table 表，将数据标记为已处理
                        String updateLogSql = "UPDATE dbo.chemical_log SET processed = 1 WHERE log_id = ?";
                        try (PreparedStatement updateLogStmt = localConnection.prepareStatement(updateLogSql)) {
                            updateLogStmt.setInt(1, logRs.getInt("log_id"));
                            updateLogStmt.executeUpdate();
                        }
                    }
                }
            } catch (SQLException | IOException e) {
                e.printStackTrace();
            }
            JavaFXApp.updateStatusLabel("日志读取任务完成");
        };

        // 定时任务：数据处理任务
        Runnable dataProcessingTask = () -> {
            JavaFXApp.updateStatusLabel("数据处理任务开始");
            try (Connection localConnection = getDatabaseConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                processChemicalData(localConnection, remoteConnection, filePath, isDateFilterActive);
            } catch (SQLException e) {
                e.printStackTrace();
            }
            JavaFXApp.updateStatusLabel("数据处理任务完成");
        };

        // 计划任务顺序执行
        executorService.scheduleWithFixedDelay(() -> {
            dataReadingTask.run();
            logReadingTask.run();
            dataProcessingTask.run();
        }, 0, 12, TimeUnit.HOURS); // 每10分钟执行一次

        // 添加JVM关闭钩子以确保资源正确释放
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("正在关闭，释放资源...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }));
    }

    private static void printConnectionPoolStats() {
        HikariDataSource cloudDataSource = dataSourceCloud;
        // 获取云数据源连接池监控信息
        HikariPoolMXBean cloudPoolMXBean = cloudDataSource.getHikariPoolMXBean();
        int cloudActiveConnections = cloudPoolMXBean.getActiveConnections();
        int cloudIdleConnections = cloudPoolMXBean.getIdleConnections();
        int cloudTotalConnections = cloudPoolMXBean.getTotalConnections();
        System.out.println("Cloud DataSource - Active: " + cloudActiveConnections + ", Idle: " + cloudIdleConnections + ", Total: " + cloudTotalConnections);
    }

    private static Connection getDatabaseConnection() throws SQLException {
        return dataSource.getConnection();
    }

    // 确保正确关闭资源
    public static void shutdown() throws PulsarClientException {
        if (consumer != null) {
            consumer.close();
        }
        if (pulsarClient != null) {
            pulsarClient.close();
        }
    }

    static class ControlLimitsCache {
        private static final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
        private static final long CACHE_EXPIRY_TIME = TimeUnit.HOURS.toMillis(24); // 缓存有效期为1天

        public static ControlLimits getControlLimits(Connection remoteConnection, String fPrcs, String fTest,
                                                     String fPart) {
            // 构建一个唯一的键来标识每个不同的参数组合
            String key = fPrcs + "_" + fTest + "_" + fPart;

            // 检查缓存
            CacheEntry entry = cache.get(key);
            if (entry != null && System.currentTimeMillis() - entry.timestamp < CACHE_EXPIRY_TIME) {
                return entry.limits;
            }

//             如果缓存中没有，则从数据库查询并更新缓存
            if (fPrcs != null && fTest != null && fPart != null) {
                String sql = "SELECT F_MEAN, F_SP FROM SPC.CTRL_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? ORDER BY F_CTRL DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
                try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
                    preparedStatement.setString(1, fPrcs);
                    preparedStatement.setString(2, fTest);
                    preparedStatement.setString(3, fPart);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            double fMean = resultSet.getDouble("F_MEAN");
                            double fSp = resultSet.getDouble("F_SP");
                            ControlLimits limits = new ControlLimits(fMean, fSp);
                            cache.put(key, new CacheEntry(limits)); // 更新缓存
                            return limits;
                        }
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }

        private static class CacheEntry {
            ControlLimits limits;
            long timestamp;

            CacheEntry(ControlLimits limits) {
                this.limits = limits;
                this.timestamp = System.currentTimeMillis();
            }
        }
    }

    private static class ControlLimits {
        double fMean;
        double fSp;

        public ControlLimits(double fMean, double fSp) {
            this.fMean = fMean;
            this.fSp = fSp;
        }
    }

    private static class CalculatedValues {
        double upperControlLimit;
        double lowerControlLimit;
        double lcl1;
        double ucl1;

        public CalculatedValues(double upperControlLimit, double lowerControlLimit, double lcl1, double ucl1) {
            this.upperControlLimit = upperControlLimit;
            this.lowerControlLimit = lowerControlLimit;
            this.lcl1 = lcl1;
            this.ucl1 = ucl1;
        }
    }

    private static void insertDataIntoChemicalYsTable(Connection connection, Chemical entity) {
        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical_ys (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,warning_upper_limit," +
                "warning_mid,warning_lower_limit,examine1_zs) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getProcessSetName());
            preparedStatement.setString(8, entity.getProcessName());
            preparedStatement.setString(9, entity.getProductSetName());
            preparedStatement.setString(10, entity.getProductName());
            preparedStatement.setString(11, entity.getTestSetName());
            preparedStatement.setString(12, entity.getTestName());
            preparedStatement.setString(13, entity.getSampleSize());
            preparedStatement.setString(14, entity.getLayerNumber());
            preparedStatement.setString(15, entity.getUpperLimit());
            preparedStatement.setString(16, entity.getMedianSpecification());
            preparedStatement.setString(17, entity.getDownLimit());
            preparedStatement.setString(18, entity.getExamine1YS());
            preparedStatement.setString(19, entity.getExamine2());
            preparedStatement.setString(20, entity.getCreatedBy());
            preparedStatement.setString(21, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(22, entity.getCreationDate());
            preparedStatement.setTimestamp(23, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 24, entity.getStatus());
            preparedStatement.setString(25, entity.getFrequency());
            preparedStatement.setString(26, entity.getFrequencyUnit());
            preparedStatement.setString(27, entity.getSlotBodyName());
            preparedStatement.setString(28, entity.getProjectTeamCode());
            preparedStatement.setString(29, entity.getProjectTeamName());
            preparedStatement.setString(30, entity.getTestCode());
            preparedStatement.setString(31, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(32, entity.getAdjustmentMid());
            preparedStatement.setString(33, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(34, entity.getProjectUnit());
            preparedStatement.setTimestamp(35, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setString(36, entity.getWarningUpperLimit());
            preparedStatement.setString(37, entity.getWarningMid());
            preparedStatement.setString(38, entity.getWarningLowerLimit());
            preparedStatement.setString(39, entity.getExamine1());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                System.out.println("应审数据成功插入。");
            }
        } catch (SQLException e) {
            System.out.println("应审插入数据时出错：" + e.getMessage());
            handleSQLException(e);
        }
    }

    public static Chemical resultToChemical(String result, String examine1) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String[] parts = result.split(",", -1); // 使用-1保留空值
        Chemical chemical = new Chemical();
        if (result != null) {
            try {
                chemical.setId(parts[0]);
                chemical.setOrganizationId(Long.valueOf(parts[1]));
                chemical.setAttributeId(Long.valueOf(parts[2]));
                chemical.setExamineDate(new Timestamp(dateFormat.parse(parts[3]).getTime()));
                chemical.setShift(parts[4]);
                chemical.setStaff(parts[5]);
                chemical.setProcessSetName(parts[6]);
                chemical.setProcessName(parts[7]);
                chemical.setProductSetName(parts[8]);
                chemical.setProductName(parts[9]);
                chemical.setTestSetName(parts[10]);
                chemical.setTestName(parts[11]);
                chemical.setSampleSize(parts[12]);
                chemical.setLayerNumber(parts[13]);
                chemical.setUpperLimit(parts[14]);
                chemical.setMedianSpecification(parts[15]);
                chemical.setDownLimit(parts[16]);
                chemical.setExamine1YS(parts[17]);
                chemical.setExamine2(parts[18]);
                chemical.setCreatedBy(parts[19]);
                chemical.setLastUpdatedBy(parts[20]);
                chemical.setCreationDate(new Timestamp(dateFormat.parse(parts[21]).getTime()));
                chemical.setLastUpdateDate(new Timestamp(dateFormat.parse(parts[22]).getTime()));
                chemical.setStatus(Long.parseLong(parts[23]));
                chemical.setFrequency(parts[24]);
                chemical.setFrequencyUnit(parts[25]);
                chemical.setSlotBodyName(parts[26]);
                chemical.setProjectTeamCode(parts[27]);
                chemical.setProjectTeamName(parts[28]);
                chemical.setTestCode(parts[29]);
                chemical.setAdjustmentUpperLimit(parts[30]);
                chemical.setAdjustmentMid(parts[31]);
                chemical.setAdjustmentLowerLimit(parts[32]);
                chemical.setProjectUnit(parts[33]);
                chemical.setWarningUpperLimit(parts[34]);
                chemical.setWarningMid(parts[35]);
                chemical.setWarningLowerLimit(parts[36]);
                chemical.setExamine1(examine1);
            } catch (Exception e) {
                e.printStackTrace();
                // Handle parsing error
            }
            return chemical;
        }
        return null;
    }
}
