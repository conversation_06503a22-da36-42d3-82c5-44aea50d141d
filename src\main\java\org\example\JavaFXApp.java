package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import org.apache.pulsar.client.api.PulsarClientException;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 状态标签
    private static TextArea logArea; // 日志显示区域
    // 按钮变量
    private static Button startButton;
    private static Button pauseButton;
    private static Button stopButton;

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("SZ302药水应审软件");

        statusLabel = new Label("当前状态：等待开始");
        statusLabel.setFont(new Font(16));

        // 创建日志显示区域
        logArea = new TextArea();
        logArea.setEditable(false);
        logArea.setPrefRowCount(15);
        logArea.setPrefColumnCount(80);
        logArea.setWrapText(true);

        // 创建按钮
        startButton = new Button("开始任务");
        pauseButton = new Button("暂停任务");
        stopButton = new Button("结束任务");

        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(20, startButton, pauseButton, stopButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(20, buttonsBox, statusLabel, logArea);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(20, 20, 20, 20));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 800, 600);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 开始任务按钮的事件处理
        startButton.setOnAction(event -> {
            // 如果路径已选择，正常启动任务
            Main.startTask();
            statusLabel.setText("当前状态：任务运行中");
            // 更新按钮状态
            updateButtonStates(true, false);
        });

        // 暂停任务按钮的事件处理
        pauseButton.setOnAction(event -> {
            Main.pauseTask();
            statusLabel.setText("当前状态：任务暂停");
            // 更新按钮状态
            updateButtonStates(true, true);
        });

        // 结束任务按钮的事件处理
        stopButton.setOnAction(event -> {
            try {
                Main.stopTask();
            } catch (PulsarClientException e) {
                throw new RuntimeException(e);
            }
            statusLabel.setText("当前状态：任务已停止");
            // 更新按钮状态
            updateButtonStates(false, false);
        });



    }

    public static void showErrorAlert(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR, message, ButtonType.OK);
            alert.setHeaderText("发生错误");
            alert.showAndWait();
        });
    }
    // 定义更新按钮状态的方法

    public static void updateButtonStates(boolean isRunning, boolean isPaused) {
        Platform.runLater(() -> {
            startButton.setDisable(isRunning);
            pauseButton.setDisable(!isRunning || isPaused);
            stopButton.setDisable(!isRunning);
        });
    }

    public static void appendLog(String message) {
        Platform.runLater(() -> {
            if (logArea != null) {
                logArea.appendText(java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + " - " + message + "\n");
                logArea.setScrollTop(Double.MAX_VALUE); // 自动滚动到底部
            }
        });
    }

    @Override
    public void stop() {
        // 设置应用程序停止标志
        Main.stopApp();

        // 等待后台线程完成
        if (Main.taskThread != null) {
            try {
                Main.taskThread.join(); // 等待线程完成执行
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                e.printStackTrace();
            }
        }
    }


    // 添加一个静态方法来更新状态标签
    public static void updateStatusLabel(String status) {
        Platform.runLater(() -> {
            statusLabel.setText(status);
        });
    }


    public static void main(String[] args) {
        launch(args);
    }
}
