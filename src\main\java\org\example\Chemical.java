package org.example;


import lombok.Data;


import java.io.Serializable;
import java.sql.Timestamp;

/**
 * (Chemical)实体类
 *
 * <AUTHOR>
 * @since 2023-12-22 16:00:58
 */

@Data
public class Chemical implements Serializable {
    private static final long serialVersionUID = -99950014482622591L;
    
    private String id;
    
    private Long organizationId;
    
    private Long attributeId;
    
    private Timestamp examineDate;
    
    private String shift;
    
    private String staff;
    
    private String departmentCode;
    
    private String processSetName;
    
    private String processName;
    
    private String productSetName;
    
    private String productName;
    
    private String testSetName;
    
    private String testName;
    
    private String sampleSize;
    
    private String layerNumber;
    
    private String upperLimit;
    
    private String medianSpecification;
    
    private String downLimit;
    
    private String examine1;

    private String examine1YS;

    private String examine2;
    
    private String createdBy;
    
    private String lastUpdatedBy;
    
    private Timestamp creationDate;
    
    private Timestamp lastUpdateDate;
    
    private Long status;
    
    private String frequency;
    
    private String frequencyUnit;
    
    private String slotBodyName;
    
    private String projectTeamCode;
    
    private String projectTeamName;
    
    private String testCode;
    
    private String adjustmentUpperLimit;
    
    private String adjustmentMid;
    
    private String adjustmentLowerLimit;
    
    private String projectUnit;

    private Boolean isExported;

    private String warningUpperLimit;

    private String warningMid;

    private String warningLowerLimit;
    private String attribute1;
    private String attribute2;
    private String attribute3;


}

